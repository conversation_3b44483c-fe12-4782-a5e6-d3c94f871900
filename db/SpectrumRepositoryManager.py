from db.repositories.spectrum.SpectrumSystemNotificationRepository import SpectrumSystemNotificationRepository
from db.repositories.spectrum.SpectrumNotificationRepository import SpectrumNotificationRepository
from db.repositories.spectrum.SpectrumAlertZoneRepository import SpectrumAlertZoneRepository
from db.repositories.spectrum.SpectrumUserRepository import SpectrumUserRepository
from db.repositories.spectrum.SpectrumAuthorizedDroneRepository import SpectrumAuthorizedDroneRepository
from db.DatabaseConnection import SpectrumDatabaseConnection
from utils.CacheService import CacheService
import logging

logger = logging.getLogger(__name__)

class SpectrumRepositoryManager:
    def __init__(self, enable_cache: bool = True):
        # Ensure single spectrum database connection across all repositories
        self.db_connection = SpectrumDatabaseConnection()
        self.enable_cache = enable_cache

        # Initialize cache service if caching is enabled
        if self.enable_cache:
            self.cache_service = CacheService()
            # Test cache connection
            if not self.cache_service.test_connection():
                logger.warning("Cache connection failed, disabling cache for this session")
                self.enable_cache = False

        # Initialize all spectrum repositories
        self._system_notification_repo = None
        self._notification_repo = None
        self._alert_zone_repo = None
        self._user_repo = None
        self._authorized_drone_repo = None

    @property
    def system_notifications(self) -> SpectrumSystemNotificationRepository:
        if self._system_notification_repo is None:
            self._system_notification_repo = SpectrumSystemNotificationRepository()
        return self._system_notification_repo

    @property
    def notifications(self) -> SpectrumNotificationRepository:
        if self._notification_repo is None:
            self._notification_repo = SpectrumNotificationRepository()
        return self._notification_repo

    @property
    def alert_zones(self) -> SpectrumAlertZoneRepository:
        if self._alert_zone_repo is None:
            self._alert_zone_repo = SpectrumAlertZoneRepository(enable_cache=self.enable_cache)
        return self._alert_zone_repo

    @property
    def users(self) -> SpectrumUserRepository:
        if self._user_repo is None:
            self._user_repo = SpectrumUserRepository(enable_cache=self.enable_cache)
        return self._user_repo

    @property
    def authorized_drones(self) -> SpectrumAuthorizedDroneRepository:
        if self._authorized_drone_repo is None:
            self._authorized_drone_repo = SpectrumAuthorizedDroneRepository(enable_cache=self.enable_cache)
        return self._authorized_drone_repo

    def close_connection(self):
        """Close the spectrum database connection"""
        self.db_connection.close_connection()
