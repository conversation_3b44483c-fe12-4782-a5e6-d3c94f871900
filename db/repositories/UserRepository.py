from db.DatabaseConnection import DatabaseConnection
from entities.User import User
from utils.CacheService import CacheService
from typing import List, Optional
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class UserRepository:

    def __init__(self, enable_cache: bool = True):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('users')
        self.user_preferences_collection = self.db_connection.get_collection('userpreferences')
        self.enable_cache = enable_cache
        if self.enable_cache:
            self.cache_service = CacheService()

    def find_for_orgs(self, orgs: str) -> List[User]:
        logger.debug(f"Finding users for org: {orgs}")

        # Try to get from cache first
        if self.enable_cache:
            cached_users_data = self.cache_service.get_users_by_org(orgs)
            if cached_users_data:
                logger.debug(f"Returning {len(cached_users_data)} cached users for org: {orgs}")
                return [User.from_dict(user_data) for user_data in cached_users_data]

        # Cache miss or caching disabled - query database
        users_cursor = self.collection.aggregate([
            {
                "$match": {
                    "orgs": orgs
                }
            },
            {
                "$lookup": {
                    "from": "userpreferences",
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_pref"
                }
            }
        ])

        users_data = list(users_cursor)
        users = [User.from_dict(user) for user in users_data]

        # Cache the results
        if self.enable_cache and users_data:
            self.cache_service.set_users_by_org(orgs, users_data)
            logger.debug(f"Cached {len(users_data)} users for org: {orgs}")

        logger.debug(f"Found {len(users)} users for org: {orgs}")
        return users

    def find_by_id(self, user_id: str) -> Optional[dict]:
        return self.collection.find_one({"_id": ObjectId(user_id)})

    def find_by_email(self, email: str) -> Optional[dict]:
        return self.collection.find_one({"email": email})

    def find_by_sub(self, sub: str) -> Optional[dict]:
        return self.collection.find_one({"sub": sub})

    def find_all_by_org_id(self, org_id: str) -> List[dict]:
        return list(self.collection.find({"org_id": org_id}))

    def save(self, user: User):
        return self.collection.insert_one(user.to_entity())

    def update(self, user_id: str, update_data: dict):
        self.collection.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": update_data}
        )

    def delete(self, user_id: str):
        self.collection.delete_one({"_id": ObjectId(user_id)})

    def find_with_preferences(self, user_id: str) -> Optional[dict]:
        users = list(self.collection.aggregate([
            {
                "$match": {
                    "_id": ObjectId(user_id)
                }
            },
            {
                "$lookup": {
                    "from": "userpreferences",
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_pref"
                }
            }
        ]))
        return users[0] if users else None

    def find_by_service_zones(self, service_zone_ids: List[str]) -> List[dict]:
        object_ids = [ObjectId(zone_id) for zone_id in service_zone_ids]
        return list(self.collection.find({
            "service_zones": {"$in": object_ids}
        }))

    def invalidate_cache_for_org(self, org_id: str):
        if self.enable_cache:
            success = self.cache_service.invalidate_users_by_org(org_id)
            if success:
                logger.debug(f"Invalidated user cache for org: {org_id}")
            else:
                logger.warning(f"Failed to invalidate user cache for org: {org_id}")

    def invalidate_user_preferences_cache(self, user_id: str):
        if self.enable_cache:
            success = self.cache_service.invalidate_user_preferences(user_id)
            if success:
                logger.debug(f"Invalidated user preferences cache for user: {user_id}")
            else:
                logger.warning(f"Failed to invalidate user preferences cache for user: {user_id}")

    def invalidate_all_user_caches(self):
        if self.enable_cache:
            deleted_count = self.cache_service.invalidate_all_user_caches()
            logger.info(f"Invalidated {deleted_count} user cache entries")
