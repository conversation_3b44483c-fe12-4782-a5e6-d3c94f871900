from db.DatabaseConnection import DatabaseConnection
from entities.SystemNotification import SystemNotification
from typing import List, Optional
from bson import ObjectId

class SystemNotificationRepository:
    
    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('system_notifications')
    
    async def save(self, notification: SystemNotification):
        return self.collection.insert_one(notification.to_entity())
    
    async def find_by_id(self, notification_id: str) -> Optional[dict]:
        return self.collection.find_one({"_id": ObjectId(notification_id)})
    
    async def find_by_org_id(self, org_id: str) -> List[dict]:
        return list(self.collection.find({"org_id": ObjectId(org_id)}))
    
    async def find_active_by_org_id(self, org_id: str) -> List[dict]:
        return list(self.collection.find({
            "org_id": ObjectId(org_id),
            "isActive": True
        }))
    
    async def mark_as_seen(self, notification_id: str, user_id: str):
        self.collection.update_one(
            {"_id": ObjectId(notification_id)},
            {"$addToSet": {"seen_by": ObjectId(user_id)}}
        )
    
    async def deactivate(self, notification_id: str):
        self.collection.update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": {"isActive": False}}
        )
