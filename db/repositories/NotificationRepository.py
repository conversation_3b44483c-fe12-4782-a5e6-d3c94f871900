from db.DatabaseConnection import DatabaseConnection
from entities.Notification import Notification
from typing import List, Optional
from bson import ObjectId

class NotificationRepository:

    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('notifications')
    
    def save(self, notification: dict):
        return self.collection.insert_one(notification)
    
    def find_by_id(self, notification_id: str) -> Optional[dict]:
        return self.collection.find_one({"_id": ObjectId(notification_id)})
    
    def find_by_org_id(self, org_id: str) -> List[dict]:
        return list(self.collection.find({"org_id": ObjectId(org_id)}))
    
    def find_by_event_id(self, event_id: str) -> List[dict]:
        return list(self.collection.find({"event_id": event_id}))
    
    def find_recent_by_alert_zone_and_event(self, alert_zone_id: str, event_id: str, hours_back: int = 6) -> List[dict]:
        return list(self.collection.find({
            "alertZone._id": alert_zone_id,
            "event_id": event_id,
            "timestamp": {
                "$gte": {"$subtract": [{"$toLong": "$$NOW"}, hours_back * 3600000]}
            }
        }))
    
    def mark_as_seen(self, notification_id: str):
        self.collection.update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": {"seen": True}}
        )
    
    def delete_by_id(self, notification_id: str):
        self.collection.delete_one({"_id": ObjectId(notification_id)})
    
    def find_unseen_by_org_id(self, org_id: str) -> List[dict]:
        return list(self.collection.find({
            "org_id": ObjectId(org_id),
            "seen": False
        }))
