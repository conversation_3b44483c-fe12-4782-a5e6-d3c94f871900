import datetime
import logging
from typing import List, Optional, Dict, Any
from bson import ObjectId
from db.DatabaseConnection import DatabaseConnection
from entities.AuthorizedDrone import AuthorizedDrone
from utils.CacheManager import CacheManager
import json
logger = logging.getLogger(__name__)

class AuthorizedDroneRepository:

    def __init__(self, enable_cache: bool = True):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('drone_authorizations')
        self.enable_cache = enable_cache

        if self.enable_cache:
            self.cache_manager = CacheManager()
            if not self.cache_manager.testConnection():
                logger.warning("Cache connection failed, disabling cache for AuthorizedDroneRepository")
                self.enable_cache = False

    def find_by_uas_id(self, uas_id: str) -> Optional[AuthorizedDrone]:
        try:
            cache_key = f"authorized_drone:uas_id:{uas_id}"

            if self.enable_cache:
                cached_data = self.cache_manager.getKey(cache_key)
                if cached_data:
                    logger.debug(f"Cache hit for authorized drone UAS ID: {uas_id}")
                    return AuthorizedDrone.from_dict(cached_data)

            drone_data = self.collection.find_one({
                "uas_id": uas_id,
                "is_active": True,
                "$or": [
                    {"expires_at": None},
                    {"expires_at": {"$gt": datetime.datetime.now(datetime.timezone.utc)}}
                ]
            })

            if drone_data:
                if self.enable_cache:
                    self.cache_manager.setKey(cache_key, drone_data, ttl=1800)

                logger.debug(f"Found authorized drone for UAS ID: {uas_id}")
                return AuthorizedDrone.from_dict(drone_data)

            logger.debug(f"No authorized drone found for UAS ID: {uas_id}")
            return None

        except Exception as e:
            logger.error(f"Error finding authorized drone by UAS ID {uas_id}: {e}")
            return None

    def find_by_org_id_and_device_id(self, org_id, device_id: str) -> Optional[AuthorizedDrone]:
        try:
            cache_key = f"authorized_drone:org_id:{org_id}:device_id:{device_id}"

            if self.enable_cache:
                cached_data = self.cache_manager.getKey(cache_key)
                if cached_data:
                    logger.debug(f"Cache hit for authorized drone device ID: {device_id}")
                    drone_data = json.loads(cached_data)
                    return AuthorizedDrone.from_dict(drone_data)

            drone_data = self.collection.aggregate([
                                                    {
                                                    "$match": {
                                                        "org_id": org_id,
                                                        "isDeleted": False,
                                                        "isActive": True
                                                        }
                                                    },
                                                    {
                                                    "$lookup": {
                                                        "from": "drones",
                                                        "localField": "drone_id",
                                                        "foreignField": "_id",
                                                        "as": "drone"
                                                        }
                                                    },
                                                    {
                                                    "$unwind": {
                                                        "path": "$drone"
                                                        }
                                                    },
                                                    {
                                                    "$match": {
                                                        "drone.device_id": device_id
                                                        }
                                                    }
                                                ])
            drone_data = list(drone_data)
            drone_data =  AuthorizedDrone.from_dict(drone_data[0]) if drone_data and len(drone_data) > 0 else None
            if drone_data:
                if self.enable_cache:
                    self.cache_manager.setJson(cache_key, drone_data.to_dict(), ttl=1800)

                logger.debug(f"Found authorized drone for device ID: {device_id}")
                return drone_data

            logger.debug(f"No authorized drone found for device ID: {device_id}")
            return None

        except Exception as e:
            logger.error(f"Error finding authorized drone by device ID {device_id}: {e}")
            return None

    def find_by_org_id(self, org_id: str) -> List[AuthorizedDrone]:
        try:
            cache_key = f"authorized_drones:org_id:{org_id}"

            if self.enable_cache:
                cached_data = self.cache_manager.getKey(cache_key)
                if cached_data:
                    logger.debug(f"Cache hit for authorized drones org ID: {org_id}")
                    return [AuthorizedDrone.from_dict(drone) for drone in cached_data]

            drones_data = list(self.collection.find({
                "org_id": ObjectId(org_id),
                "is_active": True
            }).sort("created_at", -1))

            if self.enable_cache and drones_data:
                self.cache_manager.setKey(cache_key, drones_data, ttl=1800)

            drones = [AuthorizedDrone.from_dict(drone) for drone in drones_data]
            logger.debug(f"Found {len(drones)} authorized drones for org ID: {org_id}")
            return drones

        except Exception as e:
            logger.error(f"Error finding authorized drones by org ID {org_id}: {e}")
            return []

    def find_by_id(self, drone_id: str) -> Optional[AuthorizedDrone]:
        try:
            drone_data = self.collection.find_one({"_id": ObjectId(drone_id)})

            if drone_data:
                logger.debug(f"Found authorized drone by ID: {drone_id}")
                return AuthorizedDrone.from_dict(drone_data)

            logger.debug(f"No authorized drone found for ID: {drone_id}")
            return None

        except Exception as e:
            logger.error(f"Error finding authorized drone by ID {drone_id}: {e}")
            return None

    def is_drone_authorized(self, uas_id: str, device_id: Optional[str] = None,
                           zone_id: Optional[str] = None) -> bool:
        try:
            authorized_drone = self.find_by_uas_id(uas_id)

            if not authorized_drone:
                if device_id:
                    authorized_drone = self.find_by_device_id(device_id)

            if not authorized_drone:
                return False

            if zone_id:
                return authorized_drone.is_authorized_for_zone(ObjectId(zone_id))

            return True

        except Exception as e:
            logger.error(f"Error checking drone authorization for UAS ID {uas_id}: {e}")
            return False

    def save(self, authorized_drone: AuthorizedDrone):
        try:
            authorized_drone.update_timestamp()
            result = self.collection.insert_one(authorized_drone.to_entity())

            self._invalidate_caches_for_org(str(authorized_drone.org_id))

            logger.info(f"Saved authorized drone {authorized_drone.uas_id} for org {authorized_drone.org_id}")
            return result

        except Exception as e:
            logger.error(f"Error saving authorized drone {authorized_drone.uas_id}: {e}")
            raise

    def update(self, drone_id: str, update_data: Dict[str, Any]):
        try:
            update_data['updated_at'] = datetime.datetime.now(datetime.timezone.utc)

            result = self.collection.update_one(
                {"_id": ObjectId(drone_id)},
                {"$set": update_data}
            )

            drone = self.find_by_id(drone_id)
            if drone:
                self._invalidate_caches_for_org(str(drone.org_id))
                self._invalidate_cache_for_drone(drone)

            logger.info(f"Updated authorized drone {drone_id}")
            return result

        except Exception as e:
            logger.error(f"Error updating authorized drone {drone_id}: {e}")
            raise

    def delete(self, drone_id: str):
        try:
            drone = self.find_by_id(drone_id)
            if not drone:
                logger.warning(f"Authorized drone {drone_id} not found for deletion")
                return None

            result = self.collection.update_one(
                {"_id": ObjectId(drone_id)},
                {"$set": {
                    "is_active": False,
                    "updated_at": datetime.datetime.now(datetime.timezone.utc)
                }}
            )

            self._invalidate_caches_for_org(str(drone.org_id))
            self._invalidate_cache_for_drone(drone)

            logger.info(f"Deleted authorized drone {drone_id}")
            return result

        except Exception as e:
            logger.error(f"Error deleting authorized drone {drone_id}: {e}")
            raise

    def hard_delete(self, drone_id: str):
        try:
            drone = self.find_by_id(drone_id)
            if not drone:
                logger.warning(f"Authorized drone {drone_id} not found for hard deletion")
                return None

            result = self.collection.delete_one({"_id": ObjectId(drone_id)})

            self._invalidate_caches_for_org(str(drone.org_id))
            self._invalidate_cache_for_drone(drone)

            logger.info(f"Hard deleted authorized drone {drone_id}")
            return result

        except Exception as e:
            logger.error(f"Error hard deleting authorized drone {drone_id}: {e}")
            raise

    def find_expiring_soon(self, days_ahead: int = 7) -> List[AuthorizedDrone]:
        try:
            expiry_threshold = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=days_ahead)

            drones_data = list(self.collection.find({
                "is_active": True,
                "expires_at": {
                    "$ne": None,
                    "$lte": expiry_threshold,
                    "$gt": datetime.datetime.now(datetime.timezone.utc)
                }
            }).sort("expires_at", 1))

            drones = [AuthorizedDrone.from_dict(drone) for drone in drones_data]
            logger.debug(f"Found {len(drones)} drones expiring within {days_ahead} days")
            return drones

        except Exception as e:
            logger.error(f"Error finding expiring drones: {e}")
            return []

    def cleanup_expired(self) -> int:
        try:
            result = self.collection.update_many(
                {
                    "is_active": True,
                    "expires_at": {
                        "$ne": None,
                        "$lt": datetime.datetime.now(datetime.timezone.utc)
                    }
                },
                {
                    "$set": {
                        "is_active": False,
                        "updated_at": datetime.datetime.now(datetime.timezone.utc)
                    }
                }
            )

            if result.modified_count > 0:
                self._invalidate_all_caches()
                logger.info(f"Deactivated {result.modified_count} expired authorized drones")

            return result.modified_count

        except Exception as e:
            logger.error(f"Error cleaning up expired drones: {e}")
            return 0

    def _invalidate_cache_for_drone(self, drone: AuthorizedDrone):
        if not self.enable_cache:
            return

        try:
            cache_keys = [
                f"authorized_drone:uas_id:{drone.uas_id}",
                f"authorized_drone:device_id:{drone.device_id}" if drone.device_id else None
            ]

            for key in cache_keys:
                if key:
                    self.cache_manager.delete(key)

            logger.debug(f"Invalidated cache for drone {drone.uas_id}")

        except Exception as e:
            logger.error(f"Error invalidating cache for drone {drone.uas_id}: {e}")

    def _invalidate_caches_for_org(self, org_id: str):
        if not self.enable_cache:
            return

        try:
            cache_key = f"authorized_drones:org_id:{org_id}"
            self.cache_manager.deleteKey(cache_key)
            logger.debug(f"Invalidated cache for org {org_id}")

        except Exception as e:
            logger.error(f"Error invalidating cache for org {org_id}: {e}")

    def _invalidate_all_caches(self):
        if not self.enable_cache:
            return

        try:
            patterns = [
                "authorized_drone:*",
                "authorized_drones:*"
            ]

            for pattern in patterns:
                self.cache_manager.deletePattern(pattern)

            logger.debug("Invalidated all authorized drone caches")

        except Exception as e:
            logger.error(f"Error invalidating all caches: {e}")

    def get_statistics(self, org_id: Optional[str] = None) -> Dict[str, Any]:
        try:
            match_filter = {}
            if org_id:
                match_filter["org_id"] = ObjectId(org_id)

            pipeline = [
                {"$match": match_filter},
                {
                    "$group": {
                        "_id": None,
                        "total": {"$sum": 1},
                        "active": {
                            "$sum": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$eq": ["$is_active", True]},
                                            {
                                                "$or": [
                                                    {"$eq": ["$expires_at", None]},
                                                    {"$gt": ["$expires_at", datetime.datetime.now(datetime.timezone.utc)]}
                                                ]
                                            }
                                        ]
                                    },
                                    1,
                                    0
                                ]
                            }
                        },
                        "expired": {
                            "$sum": {
                                "$cond": [
                                    {
                                        "$and": [
                                            {"$ne": ["$expires_at", None]},
                                            {"$lt": ["$expires_at", datetime.datetime.now(datetime.timezone.utc)]}
                                        ]
                                    },
                                    1,
                                    0
                                ]
                            }
                        },
                        "inactive": {
                            "$sum": {
                                "$cond": [{"$eq": ["$is_active", False]}, 1, 0]
                            }
                        }
                    }
                }
            ]

            result = list(self.collection.aggregate(pipeline))

            if result:
                stats = result[0]
                stats.pop('_id', None)
            else:
                stats = {"total": 0, "active": 0, "expired": 0, "inactive": 0}

            logger.debug(f"Retrieved authorized drone statistics: {stats}")
            return stats

        except Exception as e:
            logger.error(f"Error getting authorized drone statistics: {e}")
            return {"total": 0, "active": 0, "expired": 0, "inactive": 0}