from db.DatabaseConnection import SpectrumDatabaseConnection
from utils.CacheService import CacheService
from typing import List, Optional
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class SpectrumAlertZoneRepository:

    def __init__(self, enable_cache: bool = True):
        self.db_connection = SpectrumDatabaseConnection()
        self.collection = self.db_connection.get_collection('alertzones')
        self.enable_cache = enable_cache
        if self.enable_cache:
            self.cache_service = CacheService()

    def find_with_users_to_send(self, point: dict, event_id: str) -> List[dict]:
        lat = point['coordinates'][1]
        lng = point['coordinates'][0]
        
        cache_key = f"spectrum_alert_zones_geo_{lat}_{lng}_{event_id}"
        
        if self.enable_cache:
            cached_result = self.cache_service.get_alert_zones_by_geo(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for spectrum alert zones geo query: {cache_key}")
                return cached_result

        alert_zones_cursor = self.collection.aggregate([
            {
                "$geoNear": {
                    "near": {
                        "type": "Point",
                        "coordinates": [lng, lat]
                    },
                    "distanceField": "distance",
                    "maxDistance": 5,
                    "spherical": False
                }
            },
            {
                '$match': {
                    'isDeleted': False,
                    'isActive': True
                }
            },
            {
                "$lookup": {
                    "from": "notifications",
                    "let": { "alertZoneId": { "$toString": "$_id" } },
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {
                                            "$gte": [
                                                "$timestamp",
                                                { "$subtract": [ { "$toLong": "$$NOW" }, 21600000  ] }
                                            ]
                                        },
                                        { "$eq": ["$event_id", event_id] },
                                        { "$eq": ["$alertZone._id", "$$alertZoneId"] }
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "notifications"
                }
            },
            {
                "$lookup": {
                    "from": "organizations",
                    "localField": "orgId",
                    "foreignField": "_id",
                    "as": "organization"
                }
            },
            {
                "$match": {
                    "notifications": { "$size": 0 }
                }
            }
        ])

        alert_zones = list(alert_zones_cursor)
        
        if self.enable_cache:
            self.cache_service.set_alert_zones_by_geo(cache_key, alert_zones, ttl=300)
            logger.debug(f"Cached spectrum alert zones geo query result: {cache_key}")

        return alert_zones

    def find_by_service_zone_id(self, service_zone_id: str) -> List[dict]:
        cache_key = f"spectrum_alert_zones_service_{service_zone_id}"
        
        if self.enable_cache:
            cached_result = self.cache_service.get_alert_zones_by_service(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for spectrum alert zones service query: {cache_key}")
                return cached_result

        alert_zones_cursor = self.collection.aggregate([
            {
                '$match': {
                    'serviceZones': ObjectId(service_zone_id),
                    'isDeleted': False,
                    'isActive': True
                }
            },
            {
                "$lookup": {
                    "from": "organizations",
                    "localField": "orgId",
                    "foreignField": "_id",
                    "as": "organization"
                }
            }
        ])

        alert_zones = list(alert_zones_cursor)
        
        if self.enable_cache:
            self.cache_service.set_alert_zones_by_service(cache_key, alert_zones, ttl=300)
            logger.debug(f"Cached spectrum alert zones service query result: {cache_key}")

        return alert_zones

    async def update_status(self, alert_zone_id: str, status: int):
        result = self.collection.update_one(
            {"_id": ObjectId(alert_zone_id)},
            {"$set": {"status": status}}
        )
        logger.info(f"Updated spectrum alert zone {alert_zone_id} status to {status}")

        if self.enable_cache and result.modified_count > 0:
            alert_zone = self.find_by_id(alert_zone_id)
            if alert_zone and 'serviceZones' in alert_zone:
                for service_zone_id in alert_zone['serviceZones']:
                    self.cache_service.invalidate_alert_zones_by_service(str(service_zone_id))

            self.cache_service.invalidate_all_alert_zone_caches()
            logger.debug(f"Invalidated spectrum alert zone caches after status update: {alert_zone_id}")

    def find_by_id(self, alert_zone_id: str) -> Optional[dict]:
        return self.collection.find_one({"_id": ObjectId(alert_zone_id)})
