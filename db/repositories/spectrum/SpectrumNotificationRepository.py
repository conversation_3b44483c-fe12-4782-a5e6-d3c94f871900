from db.DatabaseConnection import SpectrumDatabaseConnection
from entities.Notification import Notification
from typing import List, Optional
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class SpectrumNotificationRepository:

    def __init__(self):
        self.db_connection = SpectrumDatabaseConnection()
        self.collection = self.db_connection.get_collection('notifications')

    async def save(self, notification: Notification):
        try:
            notification_dict = notification.to_dict()
            if '_id' in notification_dict:
                del notification_dict['_id']
            
            result = self.collection.insert_one(notification_dict)
            logger.info(f"Spectrum notification saved with ID: {result.inserted_id}")
            return result
        except Exception as e:
            logger.error(f"Error saving spectrum notification: {e}")
            raise

    def find_by_event_id(self, event_id: str) -> List[dict]:
        try:
            cursor = self.collection.find({"event_id": event_id})
            return list(cursor)
        except Exception as e:
            logger.error(f"Error finding spectrum notifications by event_id {event_id}: {e}")
            return []

    def find_by_alert_zone_id(self, alert_zone_id: str) -> List[dict]:
        try:
            cursor = self.collection.find({"alertZone._id": alert_zone_id})
            return list(cursor)
        except Exception as e:
            logger.error(f"Error finding spectrum notifications by alert_zone_id {alert_zone_id}: {e}")
            return []

    def find_by_id(self, notification_id: str) -> Optional[dict]:
        try:
            return self.collection.find_one({"_id": ObjectId(notification_id)})
        except Exception as e:
            logger.error(f"Error finding spectrum notification by ID {notification_id}: {e}")
            return None

    def find_recent_by_org(self, org_id: str, hours: int = 6) -> List[dict]:
        try:
            from datetime import datetime, timedelta
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            cursor = self.collection.find({
                "organization": org_id,
                "timestamp": {"$gte": cutoff_time}
            }).sort("timestamp", -1)
            return list(cursor)
        except Exception as e:
            logger.error(f"Error finding recent spectrum notifications for org {org_id}: {e}")
            return []

    def delete_by_id(self, notification_id: str):
        try:
            result = self.collection.delete_one({"_id": ObjectId(notification_id)})
            logger.info(f"Deleted spectrum notification {notification_id}")
            return result
        except Exception as e:
            logger.error(f"Error deleting spectrum notification: {e}")
            raise
