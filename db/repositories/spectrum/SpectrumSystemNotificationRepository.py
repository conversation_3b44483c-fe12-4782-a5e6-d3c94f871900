from db.DatabaseConnection import SpectrumDatabaseConnection
from entities.SystemNotification import SystemNotification
from typing import List, Optional
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class SpectrumSystemNotificationRepository:

    def __init__(self):
        self.db_connection = SpectrumDatabaseConnection()
        self.collection = self.db_connection.get_collection('system_notifications')

    async def save(self, notification: SystemNotification):
        try:
            notification_dict = notification.to_dict()
            if '_id' in notification_dict:
                del notification_dict['_id']
            
            result = self.collection.insert_one(notification_dict)
            logger.info(f"Spectrum system notification saved with ID: {result.inserted_id}")
            return result
        except Exception as e:
            logger.error(f"Error saving spectrum system notification: {e}")
            raise

    def find_by_org_id(self, org_id: str, limit: int = 50) -> List[dict]:
        try:
            cursor = self.collection.find(
                {"org_id": ObjectId(org_id), "isActive": True}
            ).sort("createdAt", -1).limit(limit)
            return list(cursor)
        except Exception as e:
            logger.error(f"Error finding spectrum system notifications by org_id {org_id}: {e}")
            return []

    def find_by_id(self, notification_id: str) -> Optional[dict]:
        try:
            return self.collection.find_one({"_id": ObjectId(notification_id)})
        except Exception as e:
            logger.error(f"Error finding spectrum system notification by ID {notification_id}: {e}")
            return None

    def mark_as_seen(self, notification_id: str, user_id: str):
        try:
            result = self.collection.update_one(
                {"_id": ObjectId(notification_id)},
                {"$addToSet": {"seen_by": user_id}}
            )
            logger.info(f"Marked spectrum system notification {notification_id} as seen by {user_id}")
            return result
        except Exception as e:
            logger.error(f"Error marking spectrum system notification as seen: {e}")
            raise

    def delete_by_id(self, notification_id: str):
        try:
            result = self.collection.update_one(
                {"_id": ObjectId(notification_id)},
                {"$set": {"isActive": False}}
            )
            logger.info(f"Deleted spectrum system notification {notification_id}")
            return result
        except Exception as e:
            logger.error(f"Error deleting spectrum system notification: {e}")
            raise
