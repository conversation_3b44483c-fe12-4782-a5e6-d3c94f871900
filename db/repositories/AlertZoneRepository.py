from db.DatabaseConnection import DatabaseConnection
from utils.CacheService import CacheService
from typing import List, Optional
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

class AlertZoneRepository:

    def __init__(self, enable_cache: bool = True):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('alertzones')
        self.enable_cache = enable_cache
        if self.enable_cache:
            self.cache_service = CacheService()

    def find_with_users_to_send(self, point: dict, event_id: str) -> List[dict]:
        lng = float(point["lng"]) if isinstance(point["lng"], str) else point["lng"]
        lat = float(point["lat"]) if isinstance(point["lat"], str) else point["lat"]

        logger.debug(f"Finding alert zones for coordinates: {lng}, {lat}, event: {event_id}")

        # Try to get from cache first
        # if self.enable_cache:
        #     cached_zones = self.cache_service.get_alert_zones_geo(lng, lat, event_id)
        #     if cached_zones:
        #         logger.debug(f"Returning {len(cached_zones)} cached alert zones for geo query")
        #         return cached_zones

        # Cache miss or caching disabled - query database
        alert_zones_cursor = self.collection.aggregate([
            {
                "$geoNear": {
                    "near": {
                        "type": "Point",
                        "coordinates": [lng, lat]
                    },
                    "distanceField": "distance",
                    "maxDistance": 5,
                    "spherical": False
                }
            },
            {
                '$match': {
                    'isDeleted': False,
                    'isActive': True
                }
            },
            {
                "$lookup": {
                    "from": "notifications",
                    "let": { "alertZoneId": { "$toString": "$_id" } },
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {
                                            "$gte": [
                                                "$timestamp",
                                                { "$subtract": [ { "$toLong": "$$NOW" }, 21600000  ] }
                                            ]
                                        },
                                        { "$eq": ["$event_id", event_id] },
                                        { "$eq": ["$alertZone._id", "$$alertZoneId"] }
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "notifications"
                }
            },
            {
                "$lookup": {
                    "from": "organizations",
                    "localField": "orgId",
                    "foreignField": "_id",
                    "as": "organization"
                }
            },
            {
                "$project": {
                    "organization._id": 0,
                    "notifications._id": 0,
                    "notifications.org_id": 0
                }
            }
        ])

        alert_zones = list(alert_zones_cursor)

        for alertZone in alert_zones:
            alertZone["_id"] = str(alertZone["_id"])
            alertZone["orgId"] = str(alertZone["orgId"])

        # Cache the results
        # if self.enable_cache and alert_zones:
        #     self.cache_service.set_alert_zones_geo(lng, lat, event_id, alert_zones)
        #     logger.debug(f"Cached {len(alert_zones)} alert zones for geo query")

        logger.debug(f"Found {len(alert_zones)} alert zones for coordinates: {lng}, {lat}")
        return alert_zones

    def find_by_service_zone_id(self, service_zone_id: str) -> List[dict]:
        logger.debug(f"Finding alert zones for service zone: {service_zone_id}")

        # Try to get from cache first
        if self.enable_cache:
            cached_zones = self.cache_service.get_alert_zones_by_service(service_zone_id)
            if cached_zones:
                logger.debug(f"Returning {len(cached_zones)} cached alert zones for service zone: {service_zone_id}")
                return cached_zones

        # Cache miss or caching disabled - query database
        alert_zones_cursor = self.collection.aggregate([
            {
                '$match': {
                    'isActive': True,
                    'isDeleted': False,
                    'serviceZones': ObjectId(service_zone_id)
                }
            },
            {
                '$lookup': {
                    'from': "organizations",
                    'localField': "orgId",
                    'foreignField': "_id",
                    'as': "organization"
                }
            },
            {
                '$project': {
                    'serviceZones': 0,
                    'geometry': 0,
                    'deletedAt': 0,
                    'createdAt': 0,
                    'updatedAt': 0
                }
            }
        ])

        alert_zones = list(alert_zones_cursor)

        # Cache the results
        if self.enable_cache and alert_zones:
            self.cache_service.set_alert_zones_by_service(service_zone_id, alert_zones)
            logger.debug(f"Cached {len(alert_zones)} alert zones for service zone: {service_zone_id}")

        logger.debug(f"Found {len(alert_zones)} alert zones for service zone: {service_zone_id}")
        return alert_zones

    async def update_status(self, alert_zone_id: str, status: str):
        logger.debug(f"Updating alert zone status: {alert_zone_id} -> {status}")

        # Update the database
        result = self.collection.update_one(
            {"_id": ObjectId(alert_zone_id)},
            {"$set": {"latestStatus": status}}
        )

        # Invalidate related caches
        if self.enable_cache and result.modified_count > 0:
            # Get the alert zone to find its service zones for cache invalidation
            alert_zone = self.find_by_id(alert_zone_id)
            if alert_zone and 'serviceZones' in alert_zone:
                for service_zone_id in alert_zone['serviceZones']:
                    self.cache_service.invalidate_alert_zones_by_service(str(service_zone_id))

            # Invalidate geo-based caches (this is broad but necessary since we don't know all coordinates)
            self.cache_service.invalidate_all_alert_zone_caches()
            logger.debug(f"Invalidated alert zone caches after status update: {alert_zone_id}")

    def find_by_id(self, alert_zone_id: str) -> Optional[dict]:
        return self.collection.find_one({"_id": ObjectId(alert_zone_id)})

    def find_active_by_org_id(self, org_id: str) -> List[dict]:
        return list(self.collection.find({
            "orgId": ObjectId(org_id),
            "isActive": True,
            "isDeleted": False
        }))

    def invalidate_geo_cache_for_event(self, event_id: str):
        if self.enable_cache:
            deleted_count = self.cache_service.invalidate_alert_zones_geo_pattern(event_id)
            logger.debug(f"Invalidated {deleted_count} geo cache entries for event: {event_id}")

    def invalidate_service_zone_cache(self, service_zone_id: str):
        if self.enable_cache:
            success = self.cache_service.invalidate_alert_zones_by_service(service_zone_id)
            if success:
                logger.debug(f"Invalidated service zone cache for: {service_zone_id}")
            else:
                logger.warning(f"Failed to invalidate service zone cache for: {service_zone_id}")

    def invalidate_all_caches(self):
        if self.enable_cache:
            deleted_count = self.cache_service.invalidate_all_alert_zone_caches()
            logger.info(f"Invalidated {deleted_count} alert zone cache entries")
