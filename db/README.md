# Database Repository Pattern

This directory contains the clean repository pattern implementation. The monolithic `Db<PERSON><PERSON>ler` has been completely removed and replaced with separate repository classes for each entity, all sharing a single database connection.

## Architecture Overview

### Single Database Connection

- `DatabaseConnection.py` - Singleton class that manages the MongoDB connection
- Ensures only one connection is created and shared across all repositories
- <PERSON>les connection initialization and cleanup

### Repository Classes

Each entity has its own repository class that handles all database operations for that entity:

- `SystemNotificationRepository.py` - Handles system notifications
- `NotificationRepository.py` - Handles regular notifications
- `AlertZoneRepository.py` - Handles alert zones
- `UserRepository.py` - Handles users and user preferences
- `AuthorizedDroneRepository.py` - Handles authorized/whitelisted drones

### Manager Classes

- `RepositoryManager.py` - Provides convenient access to all repositories

## Usage Patterns

### 1. Repository Manager (Recommended)

```python
from db.RepositoryManager import RepositoryManager

repo_manager = RepositoryManager()

# Access individual repositories
alert_zones = repo_manager.alert_zones.find_with_users_to_send(point, event_id)
users = repo_manager.users.find_for_orgs(org_id)
await repo_manager.system_notifications.save(notification)

# Clean up when done
repo_manager.close_connection()
```

### 2. Direct Repository Access

```python
from db.repositories.UserRepository import UserRepository
from db.repositories.AlertZoneRepository import AlertZoneRepository

# Repositories share the same connection via singleton
user_repo = UserRepository()
alert_zone_repo = AlertZoneRepository()

users = user_repo.find_for_orgs(org_id)
alert_zones = alert_zone_repo.find_by_service_zone_id(service_zone_id)
```

## Repository Methods

### SystemNotificationRepository

- `save(notification)` - Save a system notification
- `find_by_id(notification_id)` - Find by ID
- `find_by_org_id(org_id)` - Find all notifications for an organization
- `find_active_by_org_id(org_id)` - Find active notifications for an organization
- `mark_as_seen(notification_id, user_id)` - Mark as seen by user
- `deactivate(notification_id)` - Deactivate a notification

### NotificationRepository

- `save(notification)` - Save a notification
- `find_by_id(notification_id)` - Find by ID
- `find_by_org_id(org_id)` - Find all notifications for an organization
- `find_by_event_id(event_id)` - Find notifications for an event
- `find_recent_by_alert_zone_and_event()` - Find recent notifications
- `mark_as_seen(notification_id)` - Mark as seen
- `find_unseen_by_org_id(org_id)` - Find unseen notifications

### AlertZoneRepository

- `find_with_users_to_send(point, event_id)` - Geographic search with user lookup
- `find_by_service_zone_id(service_zone_id)` - Find by service zone
- `update_status(alert_zone_id, status)` - Update alert zone status
- `find_by_id(alert_zone_id)` - Find by ID
- `find_active_by_org_id(org_id)` - Find active zones for organization

### UserRepository

- `find_for_orgs(orgs)` - Find users for organizations
- `find_by_id(user_id)` - Find by ID
- `find_by_email(email)` - Find by email
- `find_by_sub(sub)` - Find by Auth0 sub
- `find_all_by_org_id(org_id)` - Find all users in organization
- `find_with_preferences(user_id)` - Find user with preferences
- `save(user)` - Save a user
- `update(user_id, update_data)` - Update user data

### AuthorizedDroneRepository

- `find_by_uas_id(uas_id)` - Find authorized drone by UAS ID
- `find_by_device_id(device_id)` - Find authorized drone by device ID
- `find_by_org_id(org_id)` - Find all authorized drones for organization
- `find_by_id(drone_id)` - Find authorized drone by ID
- `is_drone_authorized(uas_id, device_id, zone_id)` - Quick authorization check
- `save(authorized_drone)` - Save an authorized drone
- `update(drone_id, update_data)` - Update authorized drone data
- `delete(drone_id)` - Soft delete (deactivate) authorized drone
- `hard_delete(drone_id)` - Permanently delete authorized drone
- `find_expiring_soon(days_ahead)` - Find drones expiring soon
- `cleanup_expired()` - Deactivate expired authorizations
- `get_statistics(org_id)` - Get authorization statistics

## Integration with Application Classes

### AlertDistributerEvent

Uses `RepositoryManager` to access:

- `alert_zones.find_with_users_to_send()` - Find alert zones for events
- `users.find_for_orgs()` - Find users to notify
- `notifications.save()` - Save event notifications
- `authorized_drones.is_drone_authorized()` - Check if drone is authorized

### AlertDistributerIoT

Uses `RepositoryManager` to access:

- `alert_zones.find_by_service_zone_id()` - Find zones by service
- `alert_zones.update_status()` - Update zone status
- `users.find_for_orgs()` - Find users to notify
- `system_notifications.save()` - Save system notifications

### Main Application

Initializes `RepositoryManager` and passes it to distributor classes.

## Benefits

1. **Single Database Connection** - All repositories share one connection
2. **Clean Architecture** - No legacy code, pure repository pattern
3. **Separation of Concerns** - Each repository handles one entity type
4. **Better Testability** - Easier to mock individual repositories
5. **Maintainability** - Smaller, focused classes are easier to maintain
6. **Type Safety** - Better type hints and IDE support
7. **Scalability** - Easy to add new repositories for new entities

## Examples

See `examples/repository_usage_example.py` for comprehensive usage examples of all patterns.

## Testing

Run the integration tests to verify everything works correctly:

```bash
python -m unittest tests.test_repository_integration -v
```

All tests should pass, confirming that:

- Repository initialization works correctly
- Application classes integrate properly with repositories
- Database connection singleton pattern works
- All expected repository methods exist
