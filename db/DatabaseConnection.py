from pymongo import MongoClient
import os
from typing import Optional

class DatabaseConnection:
    """
    Singleton class to manage MongoDB connection.
    Ensures only one connection is created and shared across all repositories.
    """
    _instance: Optional['DatabaseConnection'] = None
    _client: Optional[MongoClient] = None
    _db = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseConnection, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._client is None:
            self._initialize_connection()

    def _initialize_connection(self):
        """Initialize the MongoDB connection"""
        mongodb_uri = os.getenv(
            "MONGODB_CONNECTION_STRING",
            "mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
        )
        mongodb_db_name = os.getenv("DATABASE_NAME", "coddn")

        self._client = MongoClient(mongodb_uri)
        self._db = self._client[mongodb_db_name]

    @property
    def client(self) -> MongoClient:
        """Get the MongoDB client"""
        if self._client is None:
            self._initialize_connection()
        return self._client

    @property
    def database(self):
        """Get the database instance"""
        if self._db is None:
            self._initialize_connection()
        return self._db

    def get_collection(self, collection_name: str):
        """Get a specific collection from the database"""
        return self.database[collection_name]

    def close_connection(self):
        """Close the database connection"""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None


class SpectrumDatabaseConnection:
    """
    Singleton class to manage Spectrum MongoDB connection.
    Ensures only one connection is created and shared across all spectrum repositories.
    """
    _instance: Optional['SpectrumDatabaseConnection'] = None
    _client: Optional[MongoClient] = None
    _db = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SpectrumDatabaseConnection, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._client is None:
            self._initialize_connection()

    def _initialize_connection(self):
        """Initialize the Spectrum MongoDB connection"""
        spectrum_uri = os.getenv(
            "SPECTRUM_CONNECTION_STRING",
            "mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority"
        )
        spectrum_db_name = os.getenv("SPECTRUM_DATABASE_NAME", "spectrum")

        self._client = MongoClient(spectrum_uri)
        self._db = self._client[spectrum_db_name]

    @property
    def client(self) -> MongoClient:
        """Get the Spectrum MongoDB client"""
        if self._client is None:
            self._initialize_connection()
        return self._client

    @property
    def database(self):
        """Get the Spectrum database instance"""
        if self._db is None:
            self._initialize_connection()
        return self._db

    def get_collection(self, collection_name: str):
        """Get a specific collection from the Spectrum database"""
        return self.database[collection_name]

    def close_connection(self):
        """Close the Spectrum database connection"""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
