import datetime
from typing import Optional, Dict, Any, List
from bson import ObjectId

class AuthorizedDrone:
    """
    AuthorizedDrone entity representing an authorized/whitelisted drone in the system.
    This class maps to the 'authorized_drones' collection in MongoDB.
    """
    
    def __init__(self,
                 org_id: ObjectId,
                 uas_id: str,
                 device_id: Optional[str] = None,
                 operator_id: Optional[str] = None,
                 manufacturer: Optional[str] = None,
                 model: Optional[str] = None,
                 serial_number: Optional[str] = None,
                 registration_number: Optional[str] = None,
                 description: Optional[str] = None,
                 is_active: bool = True,
                 authorized_zones: Optional[List[ObjectId]] = None,
                 authorized_by: Optional[str] = None,
                 authorized_at: Optional[datetime.datetime] = None,
                 expires_at: Optional[datetime.datetime] = None,
                 created_at: Optional[datetime.datetime] = None,
                 updated_at: Optional[datetime.datetime] = None,
                 metadata: Optional[Dict[str, Any]] = None,
                 _id: Optional[ObjectId] = None):
        """
        Initialize an AuthorizedDrone instance.
        
        Args:
            org_id: Organization that owns this authorized drone
            uas_id: Unique Aircraft System identifier
            device_id: Device identifier from detection system
            operator_id: Operator identifier
            manufacturer: Drone manufacturer
            model: Drone model
            serial_number: Serial number of the drone
            registration_number: Official registration number
            description: Human-readable description
            is_active: Whether the authorization is currently active
            authorized_zones: List of alert zone IDs where this drone is authorized
            authorized_by: User who authorized this drone
            authorized_at: When the drone was authorized
            expires_at: When the authorization expires (None for no expiration)
            created_at: When the record was created
            updated_at: When the record was last updated
            metadata: Additional metadata
            _id: MongoDB document ID
        """
        self._id = _id
        self.org_id = org_id
        self.uas_id = uas_id
        self.device_id = device_id
        self.operator_id = operator_id
        self.manufacturer = manufacturer
        self.model = model
        self.serial_number = serial_number
        self.registration_number = registration_number
        self.description = description
        self.is_active = is_active
        self.authorized_zones = authorized_zones or []
        self.authorized_by = authorized_by
        self.authorized_at = authorized_at or datetime.datetime.now(datetime.timezone.utc)
        self.expires_at = expires_at
        self.created_at = created_at or datetime.datetime.now(datetime.timezone.utc)
        self.updated_at = updated_at or datetime.datetime.now(datetime.timezone.utc)
        self.metadata = metadata or {}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuthorizedDrone':
        """Create an AuthorizedDrone instance from a dictionary (e.g., from MongoDB)"""
        return cls(
            _id=data.get('_id'),
            org_id=data.get('org_id'),
            uas_id=data.get('uas_id'),
            device_id=data.get('device_id'),
            operator_id=data.get('operator_id'),
            manufacturer=data.get('manufacturer'),
            model=data.get('model'),
            serial_number=data.get('serial_number'),
            registration_number=data.get('registration_number'),
            description=data.get('description'),
            is_active=data.get('is_active', True),
            authorized_zones=data.get('authorized_zones', []),
            authorized_by=data.get('authorized_by'),
            authorized_at=data.get('authorized_at'),
            expires_at=data.get('expires_at'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            metadata=data.get('metadata', {})
        )

    def to_entity(self) -> Dict[str, Any]:
        """Convert the AuthorizedDrone to a dictionary for database storage"""
        return {
            "_id": self._id,
            "org_id": self.org_id,
            "uas_id": self.uas_id,
            "device_id": self.device_id,
            "operator_id": self.operator_id,
            "manufacturer": self.manufacturer,
            "model": self.model,
            "serial_number": self.serial_number,
            "registration_number": self.registration_number,
            "description": self.description,
            "is_active": self.is_active,
            "authorized_zones": self.authorized_zones,
            "authorized_by": self.authorized_by,
            "authorized_at": self.authorized_at,
            "expires_at": self.expires_at,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "metadata": self.metadata
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert the AuthorizedDrone to a dictionary for JSON serialization"""
        return {
            "_id": str(self._id) if self._id else None,
            "org_id": str(self.org_id) if self.org_id else None,
            "uas_id": self.uas_id,
            "device_id": self.device_id,
            "operator_id": self.operator_id,
            "manufacturer": self.manufacturer,
            "model": self.model,
            "serial_number": self.serial_number,
            "registration_number": self.registration_number,
            "description": self.description,
            "is_active": self.is_active,
            "authorized_zones": [str(zone_id) for zone_id in self.authorized_zones],
            "authorized_by": self.authorized_by,
            "authorized_at": self.authorized_at.isoformat() if self.authorized_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "metadata": self.metadata
        }

    def is_expired(self) -> bool:
        """Check if the authorization has expired"""
        if not self.expires_at:
            return False
        return datetime.datetime.now(datetime.timezone.utc) > self.expires_at

    def is_authorized_for_zone(self, zone_id: ObjectId) -> bool:
        """Check if this drone is authorized for a specific alert zone"""
        if not self.is_active or self.is_expired():
            return False
        return not self.authorized_zones or zone_id in self.authorized_zones

    def update_timestamp(self):
        """Update the updated_at timestamp"""
        self.updated_at = datetime.datetime.now(datetime.timezone.utc)
