---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alert-distributor
  namespace: prod
  labels:
    app: alert-distributor
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: alert-distributor
  template:
    metadata:
      labels:
        app: alert-distributor
    spec:
      containers:
        - name: alert-distributor
          image: 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/alert-distributor:latest
          ports:
            - containerPort: 3001
          env:
            - name: ENV
              value: prod
            - name: IOT_TOPIC
              value: IOT
            - name: MONGODB_CONNECTION_STRING
              value: 'mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=AirWardenEssentials'
            - name: DATABASE_NAME
              value: 'coddn'
            - name: GROUP_ID
              value: prodGroupNotification
            - name: NOTIFICATION_TOPIC
              value: NOTIFICATION    
            - name: RID_UI_TOPIC
              value: DETECTION
            - name: RID_TOPIC
              value: RID
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: t3CNjbhxVIcPwYJbTnfXReG083LxV6yCJSsBASxn
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: 'b-1-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddnprod.9pw188.c6.kafka.us-east-2.amazonaws.com:9198'
            - name: APP_SYNC_URL
              value: https://nzf6alzxl5grfhylui4srzpfka.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-vcdnqfsqtjgbflxmr3rnhxchba
            - name: ENV_URL
              value: https://app.aerodefense.tech
            - name: ENABLE_CACHE
              value: "false"
            - name: REDIS_HOST
              value: "master.aw-e-essentials-prod1.e9ylmx.use2.cache.amazonaws.com"
            - name: REDIS_USERNAME
              value: "valkey-connect"
            - name: REDIS_PASSWORD
              value: "DyM1tcWHaNdMWAwHrnGhLMvMIa1bZ99iyWT3XXmsxSdrWQEHd45wMb2lIl8tLl3fIWO7X0UXypVXrCi2niDKy7Jlx9YZBU6OGN8JhM8IpW1N61CFbHto0XBxCb66w6dq"