---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alert-distributor
  namespace: staging
  labels:
    app: alert-distributor
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: alert-distributor
  template:
    metadata:
      labels:
        app: alert-distributor
    spec:
      containers:
        - name: alert-distributor
          image: 399444019738.dkr.ecr.us-east-2.amazonaws.com/alert-distributor:latest
          ports:
            - containerPort: 3001
          env:
            - name: ENV
              value: staging
            - name: IOT_TOPIC
              value: IOT
            - name: MONGODB_CONNECTION_STRING
              value: 'mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnStaging'
            - name: DATABASE_NAME
              value: 'coddn'
            - name: GROUP_ID
              value: stagingGroupAlertDistributor
            - name: NOTIFICATION_TOPIC
              value: NOTIFICATION  
            - name: RID_UI_TOPIC
              value: DETECTION
            - name: RID_TOPIC
              value: RID
            - name: ACCESS_KEY
              value: ********************
            - name: SECRET_KEY
              value: XB5g7U5NBtDCzd5vTXoJLumcDEuzA8sOulVO2tIg
            - name: REGION
              value: us-east-2
            - name: BOOTSTRAP_SERVERS
              value: 'b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198'
            - name: APP_SYNC_URL
              value: https://teotickbcvcu5lkpi3lcm626w4.appsync-api.us-east-2.amazonaws.com/graphql
            - name: APP_SYNC_API_KEY
              value: da2-nd5l4qk3b5ffvkwexrodozmlsi
            - name: ENV_URL
              value: https://demo.aerodefense.tech
            - name: ENABLE_CACHE
              value: "true"
            - name: REDIS_HOST
              value: "master.coddn-valkey.9nthzl.use2.cache.amazonaws.com"
            - name: REDIS_USERNAME
              value: "valkey-connect"
            - name: REDIS_PASSWORD
              value: "rfTQCauCo7B70WKb73C8TdTtWQg2GOr5"